---
title: 'PNS Design System 2.0'
subtitle: 'Led design system development and design process evolution'
date: '2023-09-20'
coverImage: '/images/thumb_pnsdesignsystem.jpg'
showOnHomepage: true
---

!worksummary[Platform: Web, iOS & Android|Timeline:1 Year|Role:Main UI/UX Designer]

### Introduction

During the year I worked at PARKnSHOP, I am tasked with creating a scalable design system that improves collaboration, ensures consistency, and makes it easier for designers to focus on delivering better customer experiences.

---

### Problem

# The original setup is not suitable for collaboration and scale

Originally there were 3 Sketch files: App, Web Desktop & Web Mobile. All assets, components, screens & flows are in those standalone files.

Here are a few key issues:

- **Styles and components were inconsistent:** For example there were 234 colours and 95 different font styles.
- **Hard to find components:** All components were on 1 page and organised without labels.
- **Hard to find screens:** All screens are on 1 page.
- **No component usage guidelines:** Hard to find and understand the correct usage of a component.
- **No code parity:** Some components look different in production and it was unclear if the production or design is outdated.

!side-by-side[UI before redesign|/images/work_pns_ds_before_1.jpg|UI after redesign|/images/work_pns_ds_before_2.jpg]

---

### Process

# Components, workflows & documentation.

1. **Migrate to Figma:** Migrating from Sketch to Figma and fix all the issues with the conversion. So that multiple designers could work collaboratively in real time.
2. **Audited, cleaned up, merged and created components and styles:** Using collaborate the team to determine and establish an initial standard.
3. **Created shared design system libraries:**: Identify similarities between platforms and create a nested design system to reduce duplication and issues.
4. **Establish design workflows:** With new designers onboarded, we worked out together a better way to collaborate effectively.
5. **Started writing documentation:** Create documentation within Figma as a source of truth.
6. Gathered feedback and continually improve

---

### Goal

# Make designing and shipping features more enjoyable. So that more time and attention is focused on making the customer experience better

---

### Libraries

# Simple and easy to understand libraries

- **Core Library:** A foundational set of design elements available across all projects.
- **Dedicated Libraries:** Separate collections for app and web designs, including typography and components.
- **Master Files:** Distinct files for app and web layouts, facilitating easier access and understanding for new designers.

We found that new designers could uinderstand where to find things without much guidance.

![Cover](/images/work_pns_ds_structure.jpg)

---

### Components

# Simplified organisation

We simplified the component hierarchy: styles (e.g., typography, colors), common components (e.g., buttons, forms, headers), and page-specific components (e.g., order summary card). Common components were grouped together, while page-specific nested components were placed close to their parent components.

![Cover](/images/work_pns_ds_1.jpg)

---

### Designer collaboration

# How designers work on features / changes

Previously, designers worked in a master file containing everything (components, screens, flows). In the new setup, each new feature or change has its own Figma file.

If a designer wanted to change a component, they wouldn’t edit the library directly. Instead, they would unlink a component and mark it so that the team knows this something to discuss.

When we have a discussion, The outcome was typically one of four:

1. Use an existing component,
2. Add a variant to an existing component
3. Improving a component
4. Creating a new component

After the feature or change launched, any new or edited components were merged into the component library, and the user flows and screens were merged into the master file.

I was aware of the Git-like version control feature available in the Organisation plan and tested it with the team. However, after I presented the feature comparisons, management decided to stick with the Pro plan.

![Cover](/images/work_pns_ds_structure_2.jpg)

---

# Outcomes

- **Enhanced Designer Productivity:** Designers now work more efficiently, leveraging the streamlined component library for quicker layout creation and iterations.
- **Consistent User Experience:** Our efforts have resulted in a more unified design language across platforms, fostering user trust and clarity.
- **Improved Collaboration:** The introduction of Figma templates has bridged the gap between designers, analysts, and engineers, to more easily keep track of project progress and documents.
- **Spearheaded company-wide Figma adoption:** Other teams saw how much more efficiently we worked, leading all UX teams at A.S. Watson to make the switch as well.

There is still a lot to be done, but in its current state, it has already benefited the team tremendously. Huge thanks to the team for their constant feedback and trust!
