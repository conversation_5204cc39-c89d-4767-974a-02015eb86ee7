---
title: 'UX Audit Framework'
subtitle: 'Actionable system to evaluate website usability & accessibility'
date: '2025-09-24'
coverImage: '/images/thumb_uxaudit.jpg'
showOnHomepage: true
---

![Image 2 (wide)](/images/work_uxaudit_cover.jpg)

# Background

While working at Dusted (a branding and digital agency) there was an increasing demand from clients for website audits. This surge in interest underscores a growing recognition of the importance of optimal user experience in digital platforms.

In response, I have developed a comprehensive UX Audit Framework designed to assist clients in conducting a thorough evaluation of their website's usability.

It covers a broad range of categories including Visual Design, Colour, WCAG 2.1, Icon Image & Illustration, and Consistency, among others, providing a structured approach to identify usability issues and areas for improvement.

&nbsp;

# Process

During the development of the framework, two distinct levels of service were established to cater to varying client needs. The first level focuses on identifying fundamental usability issues, providing a solid foundation for clients aiming to enhance the basic user experience of their websites.

The second level expands on this by incorporating the Web Content Accessibility Guidelines (WCAG) standards. This addition is aimed at clients who wish to adhere to accessibility best practices, ensuring their websites are accessible to all users, including those with disabilities.

This tiered approach allows clients to select the service level that best fits their specific requirements, whether they are looking to resolve basic usability concerns or achieve compliance with accessibility standards.

![Image](/images/work_uxaudit_1.png)

&nbsp;

# Results

The UX Audit Framework generates practical, actionable insights that clients can implement. These recommendations are organized by page and prioritized to streamline the process of enhancing their website's usability.

This structured approach ensures clients can easily identify and address the most critical issues first, facilitating immediate improvements in user experience and accessibility.

![Image](/images/work_uxaudit_2.png)
![Image](/images/work_uxaudit_3.png)

The UX Audit Framework is designed to be user-friendly, enabling individuals without a UX background to conduct audits after undergoing some training. This inclusivity broadens the scope of who can perform audits within a team, significantly enhancing the scalability of UX audit as a service.

&nbsp;

# Impact & Future

The UX Audit Framework has proven to be an invaluable resource for evaluating and improving website usability, offering immediate, actionable insights for both Dusted's clients and its internal development teams. This tool facilitates the proactive enhancement of user experiences across digital platforms.

Looking ahead, there is potential to further refine the UX audit process through the integration of emerging technologies, notably artificial intelligence (AI). The rapid advancement of AI presents an exciting opportunity to automate aspects of the UX audit, potentially making the process more efficient and comprehensive. While current AI capabilities in conducting UX audits have their limitations, Dusted remains vigilant in exploring these technological advancements. Our goal is to stay at the forefront of innovation, ensuring our UX audit services evolve alongside cutting-edge technology.
