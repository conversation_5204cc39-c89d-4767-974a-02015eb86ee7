// Site configuration
export const SITE_CONFIG = {
  name: '<PERSON>',
  title: '<PERSON> / Product Designer',
  description: 'Previously at Dusted, EY & A.S Watson',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  author: {
    name: '<PERSON>',
    email: '<EMAIL>',
    twitter: '@kevincfchu',
    linkedin: 'kevincfchu',
    github: 'kevincfchu',
  },
  location: 'London, UK',
  role: 'Digital Product',
} as const;

// Navigation configuration
export const NAVIGATION = {
  main: [
    { label: 'about', href: '/about' },
    { label: 'blog', href: '/blog' },
    {
      label: 'resume',
      href: 'https://www.dropbox.com/scl/fi/dz40v4oiq4anaj1z9sibg/<PERSON>-<PERSON>-Resume-2025.pdf?rlkey=vvawfjz3ddnby3pn150uu194r&st=98cwqrn3&dl=0',
      external: true,
    },
  ],
  social: [
    {
      label: 'LinkedIn',
      href: 'https://www.linkedin.com/in/kevincfchu/',
      icon: 'linkedin',
    },
    {
      label: 'GitHub',
      href: 'https://github.com/kevincfchu',
      icon: 'github',
    },
    {
      label: 'X (Twitter)',
      href: 'https://x.com/kevincfchu',
      icon: 'twitter',
    },
  ],
} as const;

// Theme configuration - Only dark theme is supported
export const THEME_CONFIG = {
  defaultTheme: 'dark',
  enableSystem: false, // Disable system theme detection
  storageKey: 'theme',
  attribute: 'class',
  themes: [
    {
      id: 'dark',
      label: 'Dark',
      icon: 'moon',
    },
  ],
} as const;

// Image configuration
export const IMAGE_CONFIG = {
  quality: 90,
  formats: ['image/webp', 'image/avif', 'image/jpeg'],
  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
  imageSizes: [16, 32, 64, 96, 128, 256, 384],
  placeholderSize: 10,
  blurQuality: 20,
} as const;

// Layout configuration
export const LAYOUT_CONFIG = {
  maxWidth: {
    page: '1632px',
    content: '620px',
    work: '1632px',
    workContent: '800px',
    workMid: '1200px',
  },
  margins: {
    desktop: '24px',
    tablet: '24px',
    mobile: '16px',
  },
  header: {
    height: '80px',
  },
} as const;

// Animation configuration
export const ANIMATION_CONFIG = {
  duration: {
    fast: 0.2,
    normal: 0.3,
    slow: 0.5,
    slower: 0.8,
  },
  ease: {
    default: [0.33, 1, 0.68, 1],
    smooth: [0.16, 1, 0.3, 1],
    bounce: [0.68, -0.55, 0.265, 1.55],
  },
  spring: {
    type: 'spring',
    bounce: 0,
    duration: 0.2,
  },
} as const;

// SEO configuration
export const SEO_CONFIG = {
  defaultTitle: SITE_CONFIG.title,
  titleTemplate: '%s | Kevin Chu',
  defaultDescription: SITE_CONFIG.description,
  siteUrl: SITE_CONFIG.url,
  defaultImage: '/cover.jpg',
  twitterHandle: SITE_CONFIG.author.twitter,
  locale: 'en_US',
  type: 'website',
} as const;

// Analytics configuration
export const ANALYTICS_CONFIG = {
  mixpanel: {
    token: process.env.NEXT_PUBLIC_MIXPANEL_TOKEN || '',
    enabled: process.env.NEXT_PUBLIC_ENABLE_MIXPANEL === 'true',
  },
  amplitude: {
    apiKey: '1f1ccc0bab2eae128c84a4c551fa617d',
    enabled: process.env.NEXT_PUBLIC_ENABLE_AMPLITUDE === 'true',
    sessionReplay: {
      sampleRate: 1,
    },
    autocapture: {
      elementInteractions: true,
    },
  },
  hotjar: {
    hjid: process.env.NEXT_PUBLIC_HOTJAR_ID || '6377309',
    hjsv: process.env.NEXT_PUBLIC_HOTJAR_SV || '6',
    enabled: process.env.NEXT_PUBLIC_ENABLE_HOTJAR === 'true',
  },
} as const;

// Content configuration
export const CONTENT_CONFIG = {
  postsPerPage: 10,
  worksPerPage: 12,
  excerptLength: 160,
  readingSpeed: 200, // words per minute
  dateFormat: 'MMMM dd, yyyy',
} as const;

// File paths
export const PATHS = {
  images: '/images',
  posts: '/_posts',
  work: '/_work',
  public: '/public',
  components: '/app/components',
  lib: '/app/lib',
  utils: '/app/utils',
  types: '/app/types',
  hooks: '/app/hooks',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  posts: '/api/posts',
  work: '/api/work',
  contact: '/api/contact',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  generic: 'Something went wrong. Please try again.',
  network: 'Network error. Please check your connection.',
  notFound: 'The requested resource was not found.',
  unauthorized: 'You are not authorized to access this resource.',
  validation: 'Please check your input and try again.',
  server: 'Server error. Please try again later.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  generic: 'Operation completed successfully.',
  saved: 'Changes saved successfully.',
  sent: 'Message sent successfully.',
  copied: 'Copied to clipboard.',
} as const;

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Z-index layers
export const Z_INDEX = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
} as const;

// Regular expressions
export const REGEX = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  url: /^https?:\/\/.+/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
} as const;

// File size limits
export const FILE_LIMITS = {
  image: 5 * 1024 * 1024, // 5MB
  document: 10 * 1024 * 1024, // 10MB
  video: 50 * 1024 * 1024, // 50MB
} as const;

// Supported file types
export const SUPPORTED_FILE_TYPES = {
  images: ['jpg', 'jpeg', 'png', 'webp', 'avif', 'gif', 'svg'],
  videos: ['mp4', 'webm', 'mov'],
  documents: ['pdf', 'doc', 'docx', 'txt'],
} as const;

// Cache durations (in seconds)
export const CACHE_DURATION = {
  short: 60 * 5, // 5 minutes
  medium: 60 * 60, // 1 hour
  long: 60 * 60 * 24, // 1 day
  week: 60 * 60 * 24 * 7, // 1 week
  month: 60 * 60 * 24 * 30, // 30 days
  year: 60 * 60 * 24 * 365, // 1 year
} as const;
