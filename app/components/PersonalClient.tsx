'use client';
import { motion } from 'motion/react';
import { ReactNode } from 'react';

import { SITE_CONFIG } from '@/app/constants';

import worksData from '../data/works-data.json';

import { Hero } from './Hero';
import { HomepageWorkCarousel } from './HomepageWorkCarousel';
import { StructuredData, generateStructuredData } from './SEO';

// Homepage carousel content - define your carousel images here using the markdown-like syntax
// Format: src1|alt1|caption1|src2|alt2|caption2|...
// Each item needs: image_path|alt_text|caption_text (caption can be empty but pipe is required)
// Supports both images (.jpg, .png, .webp, etc.) and videos (.mp4, .webm, etc.)
const HOMEPAGE_CAROUSEL_CONTENT =
  '/images/work_nda_dashboard.jpg|Consumer Insights Platform (NDA) |Consumer Insights Platform (NDA)|/images/work_lewissilkin_1.jpg|image 1|Law Firm Content Library Platform (UX Only)|/images/ui_xgdmedia.jpg|Startup Marketing Website|Startup Marketing Website';

// Function to parse carousel content string into images array
function parseCarouselContent(content: string) {
  // Split the content by pipe character
  const parts = content.split('|').filter((part: string) => part.trim());

  // Group parts into sets of 3 (src, alt, caption)
  const images = [];
  for (let i = 0; i < parts.length; i += 3) {
    if (parts[i] && parts[i + 1]) {
      images.push({
        src: parts[i].trim(),
        alt: parts[i + 1].trim(),
        caption: parts[i + 2] ? parts[i + 2].trim() : '',
      });
    }
  }

  return images;
}

const VARIANTS_CONTAINER = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
    },
  },
};

const VARIANTS_SECTION = {
  hidden: { opacity: 0, y: 20, filter: 'blur(8px)' },
  visible: { opacity: 1, y: 0, filter: 'blur(0px)' },
};

const TRANSITION_SECTION = {
  duration: 0.3,
};

// Props for the Personal component
interface PersonalClientProps {
  workSection: ReactNode;
}

export function PersonalClient({ workSection }: PersonalClientProps) {
  // Generate structured data for the homepage
  const structuredData = generateStructuredData({
    type: 'website',
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    url: SITE_CONFIG.url,
  });

  return (
    <>
      <StructuredData data={structuredData} />
      <motion.main
        className="space-y-24"
        variants={VARIANTS_CONTAINER}
        initial="hidden"
        animate="visible"
      >
        <motion.section variants={VARIANTS_SECTION} transition={TRANSITION_SECTION}>
          <Hero />
        </motion.section>

        <motion.section variants={VARIANTS_SECTION} transition={TRANSITION_SECTION} id="work">
          {workSection}
        </motion.section>
        {/* More Work Slideshow Section */}
        <motion.section variants={VARIANTS_SECTION} transition={TRANSITION_SECTION} id="more-work">
          <div className="homepage-wide-breakout mt-24 mb-24">
            <HomepageWorkCarousel images={parseCarouselContent(HOMEPAGE_CAROUSEL_CONTENT)} />
          </div>
        </motion.section>
      </motion.main>
    </>
  );
}


